{{ >head }}

<div
    id="dartdoc-main-content"
    class="main-content"
    data-above-sidebar="{{ aboveSidebarPath }}"
    data-below-sidebar="{{ belowSidebarPath }}">
  {{ !-- TODO(srawlins): Add annotations. }}
  {{ #self }}
    <div>
      {{ >source_link }}
      <h1>
        <span class="kind-library">{{{ displayName }}}</span>
        {{ kind }} {{ >feature_set }} {{ >categorization }}
      </h1>
    </div>
  {{ /self }}

  {{ #library }}
    {{ >documentation }}
  {{ /library }}

  {{ #library.hasPublicClasses }}
    <section class="summary offset-anchor" id="classes">
      <h2>Classes</h2>

      <dl>
        {{ #library.publicClassesSorted }}
          {{ >container }}
        {{ /library.publicClassesSorted }}
      </dl>
    </section>
  {{ /library.hasPublicClasses }}

  {{ #library.hasPublicEnums }}
    <section class="summary offset-anchor" id="enums">
      <h2>Enums</h2>

      <dl>
        {{ #library.publicEnumsSorted }}
          {{ >container }}
        {{ /library.publicEnumsSorted }}
      </dl>
    </section>
  {{ /library.hasPublicEnums }}

  {{ #library.hasPublicMixins }}
    <section class="summary offset-anchor" id="mixins">
      <h2>Mixins</h2>

      <dl>
        {{ #library.publicMixinsSorted }}
          {{ >container }}
        {{ /library.publicMixinsSorted }}
      </dl>
    </section>
  {{ /library.hasPublicMixins }}

  {{ #library.hasPublicExtensionTypes }}
    <section class="summary offset-anchor" id="extension-types">
      <h2>Extension Types</h2>

      <dl>
        {{ #library.publicExtensionTypesSorted }}
          {{ >extension_type }}
        {{ /library.publicExtensionTypesSorted }}
      </dl>
    </section>
  {{ /library.hasPublicExtensionTypes }}

  {{ #library.hasPublicExtensions }}
    <section class="summary offset-anchor" id="extensions">
      <h2>Extensions</h2>

      <dl>
        {{ #library.publicExtensionsSorted }}
          {{ >extension }}
        {{ /library.publicExtensionsSorted }}
      </dl>
    </section>
  {{ /library.hasPublicExtensions }}

  {{ #library.hasPublicConstants }}
    <section class="summary offset-anchor" id="constants">
      <h2>Constants</h2>

      <dl class="properties">
        {{ #library.publicConstantsSorted }}
          {{ >constant }}
        {{ /library.publicConstantsSorted }}
      </dl>
    </section>
  {{ /library.hasPublicConstants }}

  {{ #library.hasPublicProperties }}
    <section class="summary offset-anchor" id="properties">
      <h2>Properties</h2>

      <dl class="properties">
        {{ #library.publicPropertiesSorted }}
          {{ >property }}
        {{ /library.publicPropertiesSorted }}
      </dl>
    </section>
  {{ /library.hasPublicProperties }}

  {{ #library.hasPublicFunctions }}
    <section class="summary offset-anchor" id="functions">
      <h2>Functions</h2>

      <dl class="callables">
        {{ #library.publicFunctionsSorted }}
          {{ >callable }}
        {{ /library.publicFunctionsSorted }}
      </dl>
    </section>
  {{ /library.hasPublicFunctions }}

  {{ #library.hasPublicTypedefs }}
    <section class="summary offset-anchor" id="typedefs">
      <h2>Typedefs</h2>

      <dl>
        {{ #library.publicTypedefsSorted }}
          {{ >typedef }}
        {{ /library.publicTypedefsSorted }}
      </dl>
    </section>
  {{ /library.hasPublicTypedefs }}

  {{ #library.hasPublicExceptions }}
    <section class="summary offset-anchor" id="exceptions">
      <h2>Exceptions / Errors</h2>

      <dl>
        {{ #library.publicExceptionsSorted }}
          {{ >container }}
        {{ /library.publicExceptionsSorted }}
      </dl>
    </section>
  {{ /library.hasPublicExceptions }}

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-left" class="sidebar sidebar-offcanvas-left">
    {{ >search_sidebar }}
    <h5><span class="package-name">{{ parent.name }}</span> <span class="package-kind">{{ parent.kind }}</span></h5>
    {{ >packages }}
  </div>

  <div id="dartdoc-sidebar-right" class="sidebar sidebar-offcanvas-right">
    <h5>{{ self.name }} {{ self.kind }}</h5>
  </div><!--/sidebar-offcanvas-right-->

{{ >footer }}
