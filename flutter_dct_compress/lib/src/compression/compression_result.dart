import 'dart:typed_data';

/// Thông tin về ảnh được nén
class ImageInfo {
  /// Chiều rộng ảnh (pixels)
  final int width;
  
  /// Chiều cao ảnh (pixels)
  final int height;
  
  /// Số channels (1=grayscale, 3=RGB, 4=RGBA)
  final int channels;
  
  /// Định dạng ảnh gốc
  final String format;
  
  /// Độ sâu bit (8, 16, 24, 32)
  final int bitDepth;
  
  /// Có alpha channel không
  final bool hasAlpha;
  
  /// Color space của ảnh gốc
  final String colorSpace;
  
  /// DPI/PPI nếu có
  final double? dpi;
  
  /// Metadata EXIF nếu có
  final Map<String, dynamic>? metadata;
  
  const ImageInfo({
    required this.width,
    required this.height,
    required this.channels,
    required this.format,
    required this.bitDepth,
    required this.hasAlpha,
    required this.colorSpace,
    this.dpi,
    this.metadata,
  });
  
  /// Tính tổng số pixels
  int get totalPixels => width * height;
  
  /// Tính kích thước ước tính (bytes) cho raw data
  int get estimatedRawSize => totalPixels * channels * (bitDepth ~/ 8);
  
  /// Tỷ lệ khung hình (aspect ratio)
  double get aspectRatio => width / height;
  
  /// Kiểm tra xem có phải ảnh vuông không
  bool get isSquare => width == height;
  
  /// Kiểm tra xem có phải ảnh portrait không
  bool get isPortrait => height > width;
  
  /// Kiểm tra xem có phải ảnh landscape không
  bool get isLandscape => width > height;
  
  /// Copy với các thay đổi
  ImageInfo copyWith({
    int? width,
    int? height,
    int? channels,
    String? format,
    int? bitDepth,
    bool? hasAlpha,
    String? colorSpace,
    double? dpi,
    Map<String, dynamic>? metadata,
  }) {
    return ImageInfo(
      width: width ?? this.width,
      height: height ?? this.height,
      channels: channels ?? this.channels,
      format: format ?? this.format,
      bitDepth: bitDepth ?? this.bitDepth,
      hasAlpha: hasAlpha ?? this.hasAlpha,
      colorSpace: colorSpace ?? this.colorSpace,
      dpi: dpi ?? this.dpi,
      metadata: metadata ?? this.metadata,
    );
  }
  
  @override
  String toString() {
    return 'ImageInfo(${width}x$height, $channels channels, $format, ${bitDepth}bit)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ImageInfo &&
        other.width == width &&
        other.height == height &&
        other.channels == channels &&
        other.format == format &&
        other.bitDepth == bitDepth &&
        other.hasAlpha == hasAlpha &&
        other.colorSpace == colorSpace &&
        other.dpi == dpi;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      width,
      height,
      channels,
      format,
      bitDepth,
      hasAlpha,
      colorSpace,
      dpi,
    );
  }
}

/// Kết quả của quá trình nén ảnh
class CompressionResult {
  /// Dữ liệu ảnh đã nén
  final Uint8List compressedData;
  
  /// Tỷ lệ nén đạt được (compressed_size / original_size)
  final double compressionRatio;
  
  /// Kích thước ảnh gốc (bytes)
  final int originalSize;
  
  /// Kích thước ảnh sau nén (bytes)
  final int compressedSize;
  
  /// Thời gian xử lý
  final Duration processingTime;
  
  /// Thông tin về ảnh
  final ImageInfo imageInfo;
  
  /// ID của tác vụ nén (để tracking)
  final String taskId;
  
  /// Chất lượng đã sử dụng
  final int qualityUsed;
  
  /// Có sử dụng adaptive quantization không
  final bool usedAdaptiveQuantization;
  
  /// Số lượng DCT blocks đã xử lý
  final int processedBlocks;
  
  /// Peak Signal-to-Noise Ratio (nếu được tính)
  final double? psnr;
  
  /// Structural Similarity Index (nếu được tính)
  final double? ssim;
  
  /// Thông tin lỗi nếu có
  final String? errorMessage;
  
  /// Có thành công không
  final bool isSuccess;
  
  const CompressionResult({
    required this.compressedData,
    required this.compressionRatio,
    required this.originalSize,
    required this.compressedSize,
    required this.processingTime,
    required this.imageInfo,
    required this.taskId,
    required this.qualityUsed,
    this.usedAdaptiveQuantization = false,
    this.processedBlocks = 0,
    this.psnr,
    this.ssim,
    this.errorMessage,
    this.isSuccess = true,
  });
  
  /// Tạo CompressionResult cho trường hợp lỗi
  factory CompressionResult.error({
    required String taskId,
    required String errorMessage,
    required ImageInfo imageInfo,
    required int originalSize,
    Duration? processingTime,
  }) {
    return CompressionResult(
      compressedData: Uint8List(0),
      compressionRatio: 0.0,
      originalSize: originalSize,
      compressedSize: 0,
      processingTime: processingTime ?? Duration.zero,
      imageInfo: imageInfo,
      taskId: taskId,
      qualityUsed: 0,
      errorMessage: errorMessage,
      isSuccess: false,
    );
  }
  
  /// Tính phần trăm tiết kiệm dung lượng
  double get spaceSavingPercentage => (1.0 - compressionRatio) * 100.0;
  
  /// Tính số bytes tiết kiệm được
  int get bytesSaved => originalSize - compressedSize;
  
  /// Tính tốc độ xử lý (MB/s)
  double get processingSpeedMBps {
    if (processingTime.inMilliseconds == 0) return 0.0;
    final sizeInMB = originalSize / (1024 * 1024);
    final timeInSeconds = processingTime.inMilliseconds / 1000.0;
    return sizeInMB / timeInSeconds;
  }
  
  /// Tính tốc độ xử lý (pixels/s)
  double get processingSpeedPixelsPerSecond {
    if (processingTime.inMilliseconds == 0) return 0.0;
    final timeInSeconds = processingTime.inMilliseconds / 1000.0;
    return imageInfo.totalPixels / timeInSeconds;
  }
  
  /// Kiểm tra xem có đạt compression ratio mong muốn không
  bool isWithinCompressionBounds(double? minRatio, double? maxRatio) {
    if (minRatio != null && compressionRatio < minRatio) return false;
    if (maxRatio != null && compressionRatio > maxRatio) return false;
    return true;
  }
  
  /// Đánh giá chất lượng nén
  CompressionQuality get quality {
    if (!isSuccess) return CompressionQuality.failed;
    
    if (compressionRatio > 0.8) return CompressionQuality.low;
    if (compressionRatio > 0.5) return CompressionQuality.medium;
    if (compressionRatio > 0.3) return CompressionQuality.high;
    return CompressionQuality.excellent;
  }
  
  /// Copy với các thay đổi
  CompressionResult copyWith({
    Uint8List? compressedData,
    double? compressionRatio,
    int? originalSize,
    int? compressedSize,
    Duration? processingTime,
    ImageInfo? imageInfo,
    String? taskId,
    int? qualityUsed,
    bool? usedAdaptiveQuantization,
    int? processedBlocks,
    double? psnr,
    double? ssim,
    String? errorMessage,
    bool? isSuccess,
  }) {
    return CompressionResult(
      compressedData: compressedData ?? this.compressedData,
      compressionRatio: compressionRatio ?? this.compressionRatio,
      originalSize: originalSize ?? this.originalSize,
      compressedSize: compressedSize ?? this.compressedSize,
      processingTime: processingTime ?? this.processingTime,
      imageInfo: imageInfo ?? this.imageInfo,
      taskId: taskId ?? this.taskId,
      qualityUsed: qualityUsed ?? this.qualityUsed,
      usedAdaptiveQuantization: usedAdaptiveQuantization ?? this.usedAdaptiveQuantization,
      processedBlocks: processedBlocks ?? this.processedBlocks,
      psnr: psnr ?? this.psnr,
      ssim: ssim ?? this.ssim,
      errorMessage: errorMessage ?? this.errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }
  
  /// Chuyển đổi sang Map để serialize
  Map<String, dynamic> toMap() {
    return {
      'compressionRatio': compressionRatio,
      'originalSize': originalSize,
      'compressedSize': compressedSize,
      'processingTimeMs': processingTime.inMilliseconds,
      'taskId': taskId,
      'qualityUsed': qualityUsed,
      'usedAdaptiveQuantization': usedAdaptiveQuantization,
      'processedBlocks': processedBlocks,
      'psnr': psnr,
      'ssim': ssim,
      'errorMessage': errorMessage,
      'isSuccess': isSuccess,
      'imageInfo': {
        'width': imageInfo.width,
        'height': imageInfo.height,
        'channels': imageInfo.channels,
        'format': imageInfo.format,
        'bitDepth': imageInfo.bitDepth,
        'hasAlpha': imageInfo.hasAlpha,
        'colorSpace': imageInfo.colorSpace,
        'dpi': imageInfo.dpi,
      },
    };
  }
  
  @override
  String toString() {
    if (!isSuccess) {
      return 'CompressionResult.error(taskId: $taskId, error: $errorMessage)';
    }
    
    return 'CompressionResult('
           'ratio: ${compressionRatio.toStringAsFixed(3)}, '
           'size: ${originalSize} → ${compressedSize} bytes, '
           'quality: $qualityUsed, '
           'time: ${processingTime.inMilliseconds}ms, '
           'speed: ${processingSpeedMBps.toStringAsFixed(2)} MB/s'
           ')';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is CompressionResult &&
        other.taskId == taskId &&
        other.compressionRatio == compressionRatio &&
        other.originalSize == originalSize &&
        other.compressedSize == compressedSize &&
        other.qualityUsed == qualityUsed &&
        other.isSuccess == isSuccess;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      taskId,
      compressionRatio,
      originalSize,
      compressedSize,
      qualityUsed,
      isSuccess,
    );
  }
}

/// Enum đánh giá chất lượng nén
enum CompressionQuality {
  /// Nén thất bại
  failed,
  
  /// Chất lượng thấp (compression ratio > 0.8)
  low,
  
  /// Chất lượng trung bình (compression ratio 0.5-0.8)
  medium,
  
  /// Chất lượng cao (compression ratio 0.3-0.5)
  high,
  
  /// Chất lượng xuất sắc (compression ratio < 0.3)
  excellent,
}

/// Extension để có description cho CompressionQuality
extension CompressionQualityExtension on CompressionQuality {
  String get description {
    switch (this) {
      case CompressionQuality.failed:
        return 'Nén thất bại';
      case CompressionQuality.low:
        return 'Chất lượng thấp';
      case CompressionQuality.medium:
        return 'Chất lượng trung bình';
      case CompressionQuality.high:
        return 'Chất lượng cao';
      case CompressionQuality.excellent:
        return 'Chất lượng xuất sắc';
    }
  }
  
  String get englishDescription {
    switch (this) {
      case CompressionQuality.failed:
        return 'Failed';
      case CompressionQuality.low:
        return 'Low Quality';
      case CompressionQuality.medium:
        return 'Medium Quality';
      case CompressionQuality.high:
        return 'High Quality';
      case CompressionQuality.excellent:
        return 'Excellent Quality';
    }
  }
}
