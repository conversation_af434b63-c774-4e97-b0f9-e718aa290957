/// Flutter DCT Compress Library
///
/// Package nén ảnh thuần Dart sử dụng thuật toán Discrete Cosine Transform (DCT)
/// với hỗ trợ đa định dạng, x<PERSON> lý nền isolate, và kiểm soát tỷ lệ nén chính xác.
library;

// Export core DCT algorithm
export 'src/dct/dct_transform.dart';
export 'src/dct/quantization.dart';

// Export compression classes
export 'src/compression/compression_options.dart';
export 'src/compression/compression_result.dart';

// TODO: Export additional modules as they are implemented
// export 'src/compression/compressor.dart';
// export 'src/image/image_processor.dart';
// export 'src/isolates/compression_isolate.dart';
