import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';

void main() {
  group('DCT Transform Tests', () {
    test('DCT forward and inverse transform accuracy', () {
      // Tạo test block 8x8 với giá trị mẫu
      final testBlock = [
        [139, 144, 149, 153, 155, 155, 155, 155],
        [144, 151, 153, 156, 159, 156, 156, 156],
        [150, 155, 160, 163, 158, 156, 156, 156],
        [159, 161, 162, 160, 160, 159, 159, 159],
        [159, 160, 161, 162, 162, 155, 155, 155],
        [161, 161, 161, 161, 160, 157, 157, 157],
        [162, 162, 161, 163, 162, 157, 157, 157],
        [162, 162, 161, 161, 163, 158, 158, 158],
      ];

      // Thực hiện forward DCT
      final dctResult = DctTransform.forwardDct(testBlock);

      // Kiểm tra DC coefficient (should be around average * 8)
      expect(dctResult[0][0], greaterThan(1000));

      // Th<PERSON><PERSON> hiện inverse DCT
      final reconstructed = DctTransform.inverseDct(dctResult);

      // <PERSON><PERSON><PERSON> tra độ chính xác reconstruction
      double maxError = 0.0;
      for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
          final error = (testBlock[i][j] - reconstructed[i][j]).abs();
          maxError = maxError > error ? maxError : error.toDouble();
        }
      }

      // Error should be very small (< 1 pixel)
      expect(maxError, lessThan(1.0));
    });

    test('DCT transform validation', () {
      // Test với block đơn giản
      final simpleBlock = List.generate(8, (_) => List.filled(8, 128));

      expect(DctTransform.validateTransform(simpleBlock), isTrue);
    });

    test('Fast DCT vs standard DCT comparison', () {
      final testBlock = [
        [100, 110, 120, 130, 140, 150, 160, 170],
        [105, 115, 125, 135, 145, 155, 165, 175],
        [110, 120, 130, 140, 150, 160, 170, 180],
        [115, 125, 135, 145, 155, 165, 175, 185],
        [120, 130, 140, 150, 160, 170, 180, 190],
        [125, 135, 145, 155, 165, 175, 185, 195],
        [130, 140, 150, 160, 170, 180, 190, 200],
        [135, 145, 155, 165, 175, 185, 195, 205],
      ];

      final standardDct = DctTransform.forwardDct(testBlock);
      final fastDct = DctTransform.fastForwardDct(testBlock);

      // So sánh kết quả (should be very similar)
      double maxDiff = 0.0;
      for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
          final diff = (standardDct[i][j] - fastDct[i][j]).abs();
          maxDiff = maxDiff > diff ? maxDiff : diff;
        }
      }

      // Difference should be minimal
      expect(maxDiff, lessThan(0.1));
    });
  });

  group('Quantization Tests', () {
    test('Quantization table creation', () {
      // Test quality 50
      final table50 = Quantization.createQuantizationTable(50);
      expect(table50.length, equals(8));
      expect(table50[0].length, equals(8));

      // Test quality 100 (should have smaller values)
      final table100 = Quantization.createQuantizationTable(100);
      expect(table100[0][0], lessThan(table50[0][0]));

      // Test quality 1 (should have larger values)
      final table1 = Quantization.createQuantizationTable(1);
      expect(table1[0][0], greaterThan(table50[0][0]));
    });

    test('Quantization and dequantization', () {
      final dctBlock = [
        [1000.0, 100.0, 50.0, 25.0, 12.0, 6.0, 3.0, 1.0],
        [100.0, 80.0, 40.0, 20.0, 10.0, 5.0, 2.0, 1.0],
        [50.0, 40.0, 30.0, 15.0, 8.0, 4.0, 2.0, 1.0],
        [25.0, 20.0, 15.0, 10.0, 5.0, 3.0, 1.0, 0.0],
        [12.0, 10.0, 8.0, 5.0, 3.0, 2.0, 1.0, 0.0],
        [6.0, 5.0, 4.0, 3.0, 2.0, 1.0, 0.0, 0.0],
        [3.0, 2.0, 2.0, 1.0, 1.0, 0.0, 0.0, 0.0],
        [1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0],
      ];

      final quantTable = Quantization.createQuantizationTable(85);

      // Quantize
      final quantized = Quantization.quantize(dctBlock, quantTable);

      // Dequantize
      final dequantized = Quantization.dequantize(quantized, quantTable);

      // Check that DC coefficient is preserved reasonably well
      expect((dequantized[0][0] - dctBlock[0][0]).abs(), lessThan(50.0));
    });

    test('Adaptive quantization table creation', () {
      final adaptiveTable = Quantization.createAdaptiveQuantizationTable(
        0.5, // target compression ratio
        0.7, // image complexity
      );

      expect(adaptiveTable.length, equals(8));
      expect(adaptiveTable[0].length, equals(8));
      expect(Quantization.validateQuantizationTable(adaptiveTable), isTrue);
    });
  });

  group('Compression Options Tests', () {
    test('Default compression options', () {
      const options = CompressionOptions();

      expect(options.quality, equals(85));
      expect(options.colorSpace, equals(ColorSpace.yuv));
      expect(options.outputFormat, equals(OutputFormat.jpeg));
      expect(options.useIsolate, isTrue);
      expect(options.validate(), isTrue);
    });

    test('Compression options with bounds', () {
      final options = CompressionOptions.withBounds(
        minRatio: 0.2,
        maxRatio: 0.8,
        quality: 75,
      );

      expect(options.minCompressionRatio, equals(0.2));
      expect(options.maxCompressionRatio, equals(0.8));
      expect(options.quality, equals(75));
      expect(options.validate(), isTrue);
    });

    test('High quality preset', () {
      final options = CompressionOptions.highQuality();

      expect(options.quality, equals(95));
      expect(options.colorSpace, equals(ColorSpace.rgb));
      expect(options.useAdaptiveQuantization, isFalse);
    });

    test('High compression preset', () {
      final options = CompressionOptions.highCompression();

      expect(options.quality, equals(50));
      expect(options.colorSpace, equals(ColorSpace.yuv));
      expect(options.useAdaptiveQuantization, isTrue);
      expect(options.progressive, isTrue);
    });
  });
}
